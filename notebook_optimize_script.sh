#!/bin/bash
set -e

echo "namespace: $1"
echo "notebook name: $2"
echo "debug tool: $3"
echo "debug nbcId: $4"
echo "debug tenantId: $5"
echo "debug workspaceId: $6"

if [ -f /root/.bashrc ] && type source >/dev/null 2>&1; then
  source /root/.bashrc
fi

if [ $3 = "jupyter" ]; then
  if ! type jupyter-lab >/dev/null 2>&1; then
    echo 'jupyter un install'
    pip install jupyterlab_server jupyterlab -i https://pypi.tuna.tsinghua.edu.cn/simple
  else
    echo 'jupyter installed'
  fi
  nohup jupyter-lab --allow-root --notebook-dir=/ --ip=0.0.0.0 --no-browser --port=8888 \
    --ServerApp.token= --ServerApp.password= --ServerApp.allow_origin=* \
    --ServerApp.base_url=/v1/notebook/$1/$2 >/tmp/jupyter.log 2>&1 &
else
  echo "not jupyter tool"
fi

if [ $3 = "vscode" ]; then
  mkdir -p /home/<USER>/config/config/extensions
  if ! type code-server >/dev/null 2>&1; then
    echo 'code-server un install'
    if [ -f /etc/debian_version ]; then
      echo "debian"
      cd /home/<USER>/notebook-server
      apt install ./code-server_4.10.0_amd64.deb
      nohup code-server --bind-addr 0.0.0.0:8443 --user-data-dir /home/<USER>/config/config/data \
        --extensions-dir /home/<USER>/config/config/extensions --disable-telemetry --auth none / >/tmp/vscode.log 2>&1 &
    elif [ -f /etc/redhat-release ]; then
      echo "centos"
      cd /home/<USER>/notebook-server
      yum install -y ./code-server-4.10.0-amd64.rpm
      nohup code-server --bind-addr 0.0.0.0:8443 --user-data-dir /home/<USER>/config/config/data \
        --extensions-dir /home/<USER>/config/config/extensions --disable-telemetry --auth none / >/tmp/vscode.log 2>&1 &
    else
      echo "Unknown OS when install code-server"
      exit 1
    fi
  else
    echo 'code-server installed'
    nohup code-server --bind-addr 0.0.0.0:8443 --user-data-dir /home/<USER>/config/config/data \
      --extensions-dir /home/<USER>/config/config/extensions --disable-telemetry --auth none / >/tmp/vscode.log 2>&1 &
  fi
else
  echo "not vscode"
fi

if [ $3 = "tensorboard" ]; then
  # start 开发用，每次启动重新装包
  # rm -rf /workspace/teco_torch_tb_profiler/torch_tb_profiler
  # mkdir /workspace
  # cp -rf /home/<USER>/notebook-server/teco_torch_tb_profiler /workspace/
  # pip install -e /workspace/teco_torch_tb_profiler
  # end
  if ! type tensorboard >/dev/null 2>&1; then
    echo 'tb un install'
    pip install tensorboard==2.* -i https://pypi.mirrors.ustc.edu.cn/simple/ --trusted-host pypi.mirrors.ustc.edu.cn
  else
    echo 'tb installed'
  fi
  # 启动 tensorboard
  if tensorboard --help 2>&1 | grep -q "bind_all"; then
    nohup tensorboard --logdir=/home/<USER>/debug-optimize/tb/notebook-collection-$4 --bind_all --port 6006 >/tmp/tensorboard.log 2>&1 &
  else
    nohup tensorboard --logdir=/home/<USER>/debug-optimize/tb/notebook-collection-$4 --host=0.0.0.0 --port 6006 >/tmp/tensorboard.log 2>&1 &
  fi
else
  echo "not tb tool"
fi

if [ $3 = "ssh" ]; then
  # 安装 openssh-server
  if [ ! -f "/usr/sbin/sshd" ]; then
    echo "openssh-server 不存在，开始安装："
    if command -v apt-get &>/dev/null; then
      rm -f /etc/ssh/sshd_config
      apt-get update
      apt-get install -y openssh-server
    elif command -v yum &>/dev/null; then
      yum install -y openssh-server
    elif command -v apk &>/dev/null; then
      apk update
      apk add openssh-server
    fi
    for key in rsa ecdsa ed25519; do
      if [ ! -f "/etc/ssh/ssh_host_${key}_key" ]; then
        ssh-keygen -t $key -f "/etc/ssh/ssh_host_${key}_key" -N ''
      fi
    done
  fi
  echo "openssh-server 已安装："

  # 修改 SSH 配置
  # Check if the sshd_config file exists
  if [ -f /etc/ssh/sshd_config ]; then
    echo "Updating /etc/ssh/sshd_config..."
  else
    echo "/etc/ssh/sshd_config not found. Creating it..."
    mkdir -p /etc/ssh
    touch /etc/ssh/sshd_config
  fi
  sed -i 's/#PermitRootLogin yes/PermitRootLogin prohibit-password/g' /etc/ssh/sshd_config
  sed -i 's/PasswordAuthentication yes/PasswordAuthentication no/g' /etc/ssh/sshd_config
  sed -i 's/AuthorizedKeysFile/#AuthorizedKeysFile/g' /etc/ssh/sshd_config
  echo 'AuthorizedKeysFile     .ssh/authorized_keys /etc/authorized_keys/%u' >>/etc/ssh/sshd_config
  echo 'StrictModes no' >>/etc/ssh/sshd_config
  echo 'PubkeyAuthentication yes' >>/etc/ssh/sshd_config
  echo 'PasswordAuthentication no' >>/etc/ssh/sshd_config

  # 检查 /usr/sbin/sshd 是否存在
  if [ -x /usr/sbin/sshd ]; then
    echo "/usr/sbin/sshd found. Starting SSHD..."
    # 确保 /var/run/sshd 存在
    mkdir -p /var/run/sshd

    # groupadd sshd
    # useradd -r -g sshd -d /var/run/sshd -s /usr/sbin/nologin sshd

    # 启动 sshd
    /usr/sbin/sshd -D
  else
    echo "/usr/sbin/sshd not found. Skipping SSHD startup. Sleep infinity"
    sleep infinity
  fi
else
  echo 'not ssh'
fi

# Start the first process
#jupyter-lab --allow-root --notebook-dir=/home/<USER>/v1/notebook/$1/$2 &

# Start the second process
#code-server --bind-addr 0.0.0.0:8443 --user-data-dir /config/data --extensions-dir /config/extensions --disable-telemetry --auth none /config/workspace &

# Wait for processes to exit
# wait
# Exit with status of process that exited first
exit $?
