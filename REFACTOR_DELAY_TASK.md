# 延时任务系统重构文档

## 概述

本次重构将延时任务系统从Redis迁移到PostgreSQL数据库，简化了数据结构，增强了功能，并实现了任务取消机制。

## 主要变更

### 1. 数据库表设计

创建了新的`notebook_delay_task`表来替代Redis存储：

```sql
CREATE TABLE notebook_delay_task (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(255) NOT NULL,
    notebook_id VARCHAR(255) NOT NULL,
    nbc_id BIGINT NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    workspace_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    execute_time TIMESTAMP NOT NULL,
    status VARCHAR(50) NOT NULL,
    extra_data JSON,
    error_msg TEXT,
    update_time TIMESTAMP NOT NULL
);
```

### 2. NotebookDelayTask结构体重构

**重构前：**
```go
type NotebookDelayTask struct {
    TaskID      string       `json:"task_id"`
    NotebookID  string       `json:"notebook_id"`
    // ... 其他字段
    Snapshot    bool         `json:"snapshot"`
    Reason      string       `json:"reason"`
    Notify      NotifyPolicy `json:"notify"`
    Channel     string       `json:"channel"`
    ReceiverIDs string       `json:"receiverIDs"`
    // 使用Unix时间戳
    CreateTime  int64        `json:"create_time"`
    ExecuteTime int64        `json:"execute_time"`
}
```

**重构后：**
```go
type NotebookDelayTask struct {
    ID          int64                  `xorm:"pk autoincr"`
    TaskID      string                 `xorm:"not null varchar(255)"`
    NotebookID  string                 `xorm:"not null varchar(255)"`
    // ... 其他字段
    CreateTime  time.Time              `xorm:"not null timestamp"`
    ExecuteTime time.Time              `xorm:"not null timestamp"`
    Status      string                 `xorm:"not null varchar(50)"`
    ExtraData   map[string]interface{} `xorm:"json"`  // 动态数据存储
    ErrorMsg    string                 `xorm:"text"`
    UpdateTime  time.Time              `xorm:"not null timestamp"`
}
```

**主要改进：**
- 移除了冗余字段（Snapshot、Reason、Notify、Channel、ReceiverIDs）
- 新增`ExtraData`字段，使用PostgreSQL的JSON类型存储动态数据
- 使用`time.Time`替代Unix时间戳，提高可读性
- 新增`ErrorMsg`和`UpdateTime`字段，便于错误追踪和状态管理

### 3. 数据库操作接口

新增了以下仓库接口方法：

```go
type NotebookRepoInter interface {
    // 延时任务相关方法
    CreateDelayTask(context.Context, *model.NotebookDelayTask) error
    UpdateDelayTaskStatus(context.Context, string, string, string) error
    GetPendingDelayTasks(context.Context) ([]*model.NotebookDelayTask, error)
    CancelDelayTasksByNotebook(context.Context, string, string, string) error
    DeleteDelayTask(context.Context, string) error
    // ... 其他方法
}
```

### 4. 任务处理逻辑重构

**重构前（Redis）：**
```go
func (uc *NotebookUsecase) processDelayTasks(ctx context.Context) {
    // 使用Redis有序集合查询到期任务
    tasks, err := uc.Rds.ZRangeByScore(ctx, cons.DELAY_TASK_QUEUE_KEY, ...)
    // 从Redis Hash获取任务数据
    taskData, err := uc.Rds.HGet(ctx, taskKey, "data").Result()
    // JSON反序列化
    json.Unmarshal([]byte(taskData), &task)
    // 更新Redis状态
    uc.Rds.HSet(ctx, taskKey, "status", "processing")
}
```

**重构后（PostgreSQL）：**
```go
func (uc *NotebookUsecase) processDelayTasks(ctx context.Context) {
    // 直接从数据库查询到期的待处理任务
    tasks, err := uc.nbRepo.GetPendingDelayTasks(ctx)
    // 检查任务状态，跳过已取消的任务
    if task.Status == "cancelled" {
        continue
    }
    // 更新数据库中的任务状态
    uc.nbRepo.UpdateDelayTaskStatus(ctx, task.TaskID, "processing", "")
    // 从ExtraData获取动态数据
    snapshot := task.ExtraData["snapshot"].(bool)
}
```

### 5. 任务取消功能

新增了完整的任务取消机制：

**在ReleaseIdleNotebook函数中：**
```go
// 处理任务取消逻辑（当alert.Status为"resolved"时）
if alert.Status == consEnum.Resolved.String() {
    // 取消对应的延时任务
    err := uc.nbRepo.CancelDelayTasksByNotebook(ctx, nbId, tenantIdStr, wIdStr)
    // ...
}
```

**在processDelayTasks函数中：**
```go
// 检查任务状态，跳过已取消的任务
if task.Status == "cancelled" {
    uc.Log.Infof("跳过已取消的任务: %v", task.TaskID)
    continue
}
```

### 6. 数据迁移策略

由于数据结构发生了重大变化，建议采用以下迁移策略：

1. **部署新版本**：新版本将使用数据库存储新的延时任务
2. **Redis数据处理**：现有Redis中的任务将继续由旧逻辑处理直到完成
3. **逐步清理**：Redis中的任务数据会自然过期（30天TTL）

## 技术优势

### 1. 数据一致性
- 使用PostgreSQL事务保证数据一致性
- 避免Redis数据丢失风险

### 2. 查询性能
- 数据库索引优化查询性能
- 支持复杂查询条件

### 3. 可扩展性
- JSON字段支持动态数据存储
- 便于添加新的任务属性

### 4. 运维友好
- 统一数据存储，简化运维
- 支持SQL查询和分析

### 5. 功能增强
- 完整的任务取消机制
- 详细的错误信息记录
- 任务状态追踪

## 测试验证

创建了单元测试文件`internal/biz/notebook_delay_task_test.go`来验证：
- 延时任务创建
- 任务存储
- 状态更新
- 取消机制

## 向后兼容性

- 保持了原有的API接口不变
- 新的ExtraData字段向后兼容原有的通知逻辑
- 渐进式迁移，不影响现有功能

## 数据库索引优化

为了提高查询性能，已在关键字段上添加了索引：

```sql
-- 主要索引
CREATE UNIQUE INDEX idx_task_id ON notebook_delay_task(task_id);
CREATE INDEX idx_notebook_id ON notebook_delay_task(notebook_id);
CREATE INDEX idx_tenant_id ON notebook_delay_task(tenant_id);
CREATE INDEX idx_workspace_id ON notebook_delay_task(workspace_id);
CREATE INDEX idx_execute_time ON notebook_delay_task(execute_time);
CREATE INDEX idx_status ON notebook_delay_task(status);

-- 复合索引（用于任务取消查询）
CREATE INDEX idx_notebook_tenant_workspace_status ON notebook_delay_task(notebook_id, tenant_id, workspace_id, status);

-- 复合索引（用于待处理任务查询）
CREATE INDEX idx_status_execute_time ON notebook_delay_task(status, execute_time);
```

## 部署注意事项

1. **数据库迁移**：确保新表已创建并添加了索引
2. **配置检查**：验证数据库连接配置
3. **监控**：关注任务执行状态和错误日志
4. **回滚计划**：准备回滚到Redis版本的方案（如需要）
5. **性能监控**：监控数据库查询性能，特别是GetPendingDelayTasks查询

## 使用示例

### 创建延时任务
```go
extraData := map[string]interface{}{
    "snapshot":    true,
    "notify":      notifyPolicy,
    "channel":     "0,1", // 邮箱+WEBHOOK
    "receiverIDs": "user1,user2",
}

task := model.NewNotebookDelayTask(
    "notebook-123", 456, "tenant-1", "workspace-1", "user-1",
    time.Now().Add(30*time.Minute), "pending", extraData)

err := uc.nbRepo.CreateDelayTask(ctx, task)
```

### 取消任务
```go
err := uc.nbRepo.CancelDelayTasksByNotebook(ctx, notebookID, tenantID, workspaceID)
```

### 查询待处理任务
```go
tasks, err := uc.nbRepo.GetPendingDelayTasks(ctx)
```

## 监控指标

建议监控以下指标：

1. **任务创建速率**：每分钟创建的延时任务数量
2. **任务执行成功率**：成功执行的任务比例
3. **任务执行延迟**：任务实际执行时间与预定时间的差异
4. **数据库查询性能**：GetPendingDelayTasks查询的执行时间
5. **任务状态分布**：各种状态的任务数量分布

## 故障排查

### 常见问题

1. **任务不执行**
   - 检查processDelayTasks是否正常运行
   - 检查数据库连接是否正常
   - 检查任务状态是否为pending

2. **任务执行失败**
   - 查看error_msg字段中的错误信息
   - 检查PatchNotebook操作的相关日志

3. **任务取消失败**
   - 检查CancelDelayTasksByNotebook的调用参数
   - 确认任务状态是否为pending

### 日志关键字

搜索以下关键字来排查问题：
- "Store delay task"
- "Update delay task status"
- "Cancel delay tasks"
- "跳过已取消的任务"
- "执行延时任务PatchNotebook"

## 后续优化建议

1. **性能优化**：
   - 监控数据库查询性能
   - 考虑分区表（按时间分区）
   - 定期清理已完成的历史任务

2. **监控告警**：
   - 添加任务执行监控和告警
   - 监控任务积压情况
   - 监控执行失败率

3. **功能增强**：
   - 支持批量任务操作
   - 实现失败任务的重试机制
   - 添加任务优先级支持

4. **运维工具**：
   - 提供任务管理界面
   - 支持手动触发任务执行
   - 提供任务统计报表
