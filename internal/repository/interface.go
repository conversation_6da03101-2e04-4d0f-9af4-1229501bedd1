package repository

import (
	"context"

	pbNb "git-plat.tecorigin.net/ai-platform/notebook-server/api/notebook/notebook"
	pb "git-plat.tecorigin.net/ai-platform/notebook-server/api/notebook/v1"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/data"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/model"

	"github.com/nats-io/nats.go/jetstream"
	"github.com/xormplus/xorm"
)

type IMQRepository interface {
	// one consumber -> multi topic
	// subscribe from the now, neglect the history messages.
	Subscribe(ctx context.Context, customer string, topic string,
		handler jetstream.MessageHandler) error
	Public(ctx context.Context, subject string, msg interface{}) error
}

type NotebookRepoInter interface {
	// notebook
	Create(context.Context, *model.Notebook, *xorm.Session) (int64, error)
	DeleteNotebook(context.Context, *model.Notebook, *xorm.Session) error
	UpdateNotebook(context.Context, *model.Notebook) error
	UpdateNotebookFull(context.Context, *model.Notebook) error
	UpdateNotebookName(context.Context, *pb.NotebookNameRequest) error
	UpdateNbImageType(context.Context, *pb.CreateNotebookRequest) error
	UpdateNbStatusById(string, string) error
	GetSession() *xorm.Session
	GetRepoData() *data.Data
	GetNotebookByName(context.Context, *model.Notebook) (*model.Notebook, error)
	CheckNotebookDisplayName(context.Context, string, int64, int64) (int, error)
	GetNotebookById(context.Context, string) (*model.Notebook, error)
	GetNotebookByNbcId(context.Context, int64) ([]*model.Notebook, error)
	CountNotebookInWorkspace(
		context.Context, *model.Notebook, string, *pbNb.ListNotebookRequest) (
		int64, error)
	CountNotebookInWorkspaces(context.Context, []uint64) (int64, error)
	ListNbListInWorkspace(
		context.Context, *model.Notebook, string, *pbNb.ListNotebookRequest) (
		[]*model.Notebook, error)
	// todo: meld into the former interface.
	ListNotebooksInWorkspace(context.Context, *model.Notebook, string) ([]*model.Notebook, error)
	ListNotebookInWorkspaces(context.Context, []uint64, *model.Pagination) ([]*model.Notebook, error)
	GetNbByIds(context.Context, []interface{}) ([]*model.Notebook, error)
	GetNotebookDisplayNameById(context.Context, []interface{}) ([]*model.Notebook, error)
	GetNotebooksList(workspaceId int64, nbType string) ([]*model.Notebook, error)

	// delay task
	CreateDelayTask(context.Context, *model.NotebookDelayTask) error
	DeleteDelayTask(context.Context, string) error
	UpdateDelayTaskStatus(context.Context, string, string, string) error
	CancelDelayTasksByNotebook(context.Context, string, string, string) error
	GetPendingDelayTasks(context.Context) ([]*model.NotebookDelayTask, error)

	// collection
	CreateCollection(context.Context, *model.NotebookCollection, *xorm.Session) (int64, error)
	DeleteNbCollection(context.Context, *pb.DeleteNotebookCollectionRequest, *xorm.Session) error
	UpdateCollection(context.Context, *model.NotebookCollection, *xorm.Session, string, string) (int64, error)
	CheckNbCollectionName(context.Context, *pb.CheckNotebookCollectionNameRequest, int64) (int, error)
	CountCollection(context.Context, *pb.ListNotebookCollectionRequest, int64) (int, error)
	Creators(ctx context.Context, workspaceId int64) ([]*model.Creator, error)
	GetNBCollectionByID(context.Context, *model.NotebookCollection, *xorm.Session) (
		*model.NotebookCollection, error)
	ListNbCollection(context.Context, *pb.ListNotebookCollectionRequest, int) (
		[]*model.NotebookCollection, error)

	// spec configuration
	SaveNbProductCommoditySpec(context.Context, *model.NbProductCommoditySpecRelation) error
	GetNbProductCommoditySpecByNotebookId(context.Context, int64) (*model.NbProductCommoditySpecRelation, error)
	UpdateNbProductCommoditySpecByNotebookId(context.Context, *model.NbProductCommoditySpecRelation) error
	DeleteNbProductCommoditySpecByNotebookId(context.Context, int64) error

	SaveSpecConfiguration(context.Context, *model.NbSpecConfigurationRelation) error
	BatchSaveSpecConfigurations(context.Context, []*model.NbSpecConfigurationRelation) error
	DeleteSpecConfigurationByNotebookId(context.Context, int64) error
	GetSpecConfigurationByNotebookId(context.Context, int64) ([]*model.NbSpecConfigurationRelation, error)
}
