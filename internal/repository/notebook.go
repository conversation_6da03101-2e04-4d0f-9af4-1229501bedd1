package repository

import (
	"context"
	"fmt"

	err_v1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	pbNb "git-plat.tecorigin.net/ai-platform/notebook-server/api/notebook/notebook"
	pb "git-plat.tecorigin.net/ai-platform/notebook-server/api/notebook/v1"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/cons"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/data"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/model"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/util"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/xormplus/builder"
	"github.com/xormplus/xorm"
)

type NotebookRepoImpl struct {
	Data *data.Data
	Log  *log.Helper
}

func NewNotebookRepo(data *data.Data, h *log.Helper) NotebookRepoInter {
	return &NotebookRepoImpl{
		Data: data,
		Log:  h,
	}
}

func (n *NotebookRepoImpl) GetSession() *xorm.Session {
	return n.Data.Engine.NewSession()
}

func (n *NotebookRepoImpl) Create(
	ctx context.Context, nb *model.Notebook, session *xorm.Session) (
	int64, error) {

	affected, err := session.InsertOne(nb)
	if err != nil {
		n.Log.Errorf("Create notebook in db failed: %v", err)
		return 0, err
	}
	n.Log.Infof("create notebook id:%v, affected: %v", nb.Id, affected)
	return nb.Id, nil
}

func (n *NotebookRepoImpl) UpdateNotebook(
	ctx context.Context, nb *model.Notebook) error {

	_, err := n.Data.Engine.ID(nb.Id).Update(nb)
	if err != nil {
		return err_v1.ErrorDbError(err.Error())
	}
	return nil
}

func (n *NotebookRepoImpl) UpdateNotebookFull(
	ctx context.Context, nb *model.Notebook) error {

	_, err := n.Data.Engine.ID(nb.Id).AllCols().Update(nb)
	if err != nil {
		return err_v1.ErrorDbError(err.Error())
	}
	return nil
}

func (n *NotebookRepoImpl) UpdateNotebookName(
	ctx context.Context, req *pb.NotebookNameRequest) error {

	update, err := n.Data.Engine.SQL(
		"update notebook set display_name = ? where id = ?",
		req.Name, req.Id).Execute()
	if err != nil {
		return err
	}
	id, _ := update.LastInsertId()
	affected, _ := update.RowsAffected()
	n.Log.Infof("update notebook name. id:%v, affected:%v", id, affected)
	return nil
}

func (n *NotebookRepoImpl) GetNotebookById(
	ctx context.Context, notebookId string) (*model.Notebook, error) {

	var ret model.Notebook
	_, err := n.Data.Engine.SQL(
		"select * from notebook where id = ?", notebookId).Get(&ret)
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (n *NotebookRepoImpl) GetNotebookByName(
	ctx context.Context, nb *model.Notebook) (*model.Notebook, error) {

	sql := `select * from notebook where workspace_id = ? and name = ?`
	var ret model.Notebook
	_, err := n.Data.Engine.SQL(sql, nb.WorkspaceId, nb.Name).Get(&ret)
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (n *NotebookRepoImpl) CheckNotebookDisplayName(
	ctx context.Context, name string, workspaceId, nbcId int64) (int, error) {
	var (
		count int
		err   error
	)
	if nbcId == 0 {
		_, err = n.Data.Engine.SQL(
			"select count(*) from notebook where workspace_id = ? and display_name = ?",
			workspaceId, name).Get(&count)
	} else {
		_, err = n.Data.Engine.SQL(
			"select count(*) from notebook where workspace_id = ? and display_name = ? and nbc_id = ?",
			workspaceId, name, nbcId).Get(&count)
	}
	if err != nil {
		return -1, err
	}
	return count, nil
}

func (n *NotebookRepoImpl) CountNotebookInWorkspace(
	ctx context.Context, nb *model.Notebook, nbType string,
	req *pbNb.ListNotebookRequest) (
	int64, error) {

	var (
		count  int64
		err    error
		sqlStr string
	)
	sqlStr = "select count(*) from notebook where workspace_id = ?"
	if req.Id != "" {
		sqlStr = fmt.Sprintf("%v and id = %v", sqlStr, req.Id)
	}
	if nbType == cons.NOTEBOOK {
		sqlStr += " and nbc_id = 0"
	} else if nbType == cons.OPTIMIZE {
		if nb.NbcId != 0 {
			sqlStr = fmt.Sprintf(" %v and nbc_id = %v", sqlStr, nb.NbcId)
		} else {
			sqlStr += " and nbc_id <> 0"
		}
	}
	if req.Name != "" && nbType == cons.NOTEBOOK {
		sqlStr = fmt.Sprintf("%v and  (display_name  ~* '%v'   or creator  ~* '%v' )",
			sqlStr, util.ParamsFiltering(req.Name), util.ParamsFiltering(req.Name))
	} else if req.Name != "" && nbType == cons.OPTIMIZE {
		sqlStr = fmt.Sprintf("%v and  display_name  ~* '%v'", sqlStr, util.ParamsFiltering(req.Name))
	}
	if len(req.NotebookStatus) > 0 {
		sqlStr += " and ("
		for i := 0; i < len(req.NotebookStatus); i++ {
			if len(req.NotebookStatus)-i > 1 {
				sqlStr += " status = '" + req.NotebookStatus[i] + "' or"
			} else {
				sqlStr += " status = '" + req.NotebookStatus[i] + "'"
			}
		}
		sqlStr += " )"
	}

	_, err = n.Data.Engine.SQL(sqlStr, nb.WorkspaceId).Get(&count)
	if err != nil {
		return -1, err
	}
	return count, nil
}

func (n *NotebookRepoImpl) CountNotebookInWorkspaces(
	ctx context.Context, workspaceIds []uint64) (int64, error) {

	var count int64
	if len(workspaceIds) == 0 {
		return 0, nil
	}

	count, err := n.Data.Engine.Table(cons.TableNotebook).Where("nbc_id != 0").In(
		"workspace_id", workspaceIds).Count()
	if err != nil {
		return 0, err_v1.ErrorDbError(err.Error())
	}
	return count, nil
}

func (n *NotebookRepoImpl) ListNbListInWorkspace(
	ctx context.Context, nb *model.Notebook, nbType string,
	req *pbNb.ListNotebookRequest) ([]*model.Notebook, error) {

	var (
		nbList []*model.Notebook
		err    error
	)
	sqlStr := "select * from notebook where workspace_id = ?"
	if req.Id != "" {
		sqlStr = fmt.Sprintf("%v and id = %v", sqlStr, req.Id)
	}
	if nbType == cons.NOTEBOOK {
		sqlStr += " and nbc_id = 0"
	} else if nbType == cons.OPTIMIZE {
		if nb.NbcId != 0 {
			sqlStr = fmt.Sprintf(" %v and nbc_id = %v", sqlStr, nb.NbcId)
		} else {
			sqlStr += " and nbc_id <> 0"
		}
	}
	if req.Name != "" && nbType == cons.NOTEBOOK {
		sqlStr = fmt.Sprintf("%v and  (display_name  ~* '%v'   or creator  ~* '%v' )",
			sqlStr, util.ParamsFiltering(req.Name), util.ParamsFiltering(req.Name))
	} else if req.Name != "" && nbType == cons.OPTIMIZE {
		sqlStr = fmt.Sprintf("%v and  display_name  ~* '%v'", sqlStr, req.Name)
	}
	if len(req.NotebookStatus) > 0 {
		sqlStr += " and ("
		for i := 0; i < len(req.NotebookStatus); i++ {
			if len(req.NotebookStatus)-i > 1 {
				sqlStr += " status = '" + req.NotebookStatus[i] + "' or"
			} else {
				sqlStr += " status = '" + req.NotebookStatus[i] + "'"
			}
		}
		sqlStr += " )"
	}
	//判断是否分页
	if req.CurrentPage != 0 && req.PageSize != 0 {
		sqlStr += " order by create_time desc"
		sqlStr += " limit ? OFFSET ?"
		err = n.Data.Engine.SQL(
			sqlStr, nb.WorkspaceId, req.PageSize, (req.CurrentPage-1)*req.PageSize).Find(&nbList)
	} else {
		err = n.Data.Engine.SQL(sqlStr, nb.WorkspaceId).Find(&nbList)
	}
	if err != nil {
		return nil, err
	}

	return nbList, nil
}

func (n *NotebookRepoImpl) ListNotebooksInWorkspace(
	ctx context.Context, nb *model.Notebook, nbType string) (
	[]*model.Notebook, error) {

	var (
		nbList []*model.Notebook
		err    error
	)
	sqlStr := `select * from notebook where workspace_id = ?`
	args := []interface{}{nb.WorkspaceId}

	if nbType == cons.NOTEBOOK {
		sqlStr += " and nbc_id = 0 "
		if nb.Id != 0 {
			sqlStr += " and id = ?"
			args = append(args, nb.Id)
		}
	} else if nbType == cons.OPTIMIZE {
		if nb.NbcId != 0 {
			sqlStr += " and nbc_id = ?"
			args = append(args, nb.NbcId)
		} else {
			sqlStr += " and nbc_id <> 0"
		}
	} else {
		return nbList, nil
	}
	err = n.Data.Engine.SQL(sqlStr, args...).Find(&nbList)

	if err != nil {
		return nil, err
	}
	return nbList, nil
}

func (n *NotebookRepoImpl) ListNotebookInWorkspaces(
	ctx context.Context, workspaceIds []uint64, pagination *model.Pagination) (
	[]*model.Notebook, error) {

	var nbList []*model.Notebook
	if len(workspaceIds) == 0 {
		return nbList, nil
	}

	err := n.Data.Engine.Where("nbc_id != 0").In("workspace_id", workspaceIds).OrderBy(
		pagination.Sort+" "+pagination.Order.String()).Limit(
		int(pagination.PageSize), int((pagination.CurrentPage-1)*pagination.PageSize)).Find(
		&nbList)
	if err != nil {
		return nil, err
	}
	return nbList, nil
}

func (n *NotebookRepoImpl) UpdateNbStatusById(status, nbId string) error {
	update, err := n.Data.Engine.SQL(
		"update notebook set status = ? where id = ?", status, nbId).Execute()
	if err != nil {
		return err
	}
	id, _ := update.LastInsertId()
	affected, _ := update.RowsAffected()
	n.Log.Infof("update notebook status. id:%v, affected:%v", id, affected)
	return nil
}

func (n *NotebookRepoImpl) GetNbByIds(
	ctx context.Context, iDs []interface{}) ([]*model.Notebook, error) {

	var (
		nbList []*model.Notebook
		err    error
	)
	sqlStr := builder.Dialect(builder.POSTGRES).Select(
		"id", "display_name", "namespace", "workspace_id", "project_id", "name",
		"nbc_id", "image_type", "status", "image", "num", "cpu", "memory",
		"architecture", "gpu_num", "gpu_vendor").Where(
		builder.In("id", iDs)).From(cons.NOTEBOOK_TABLE_NAME)
	err = n.Data.Engine.SQL(sqlStr).Find(&nbList)
	//err = r.Data.Engine.SQL("select id,  from notebook").In("id", iDs...).Find(&nbList)
	if err != nil {
		return nil, err_v1.ErrorDbError(err.Error())
	}
	return nbList, nil
}

func (n *NotebookRepoImpl) GetNotebookDisplayNameById(
	ctx context.Context, iDs []interface{}) ([]*model.Notebook, error) {

	var (
		nbList []*model.Notebook
		err    error
	)
	sqlStr := builder.Dialect(builder.POSTGRES).Select(
		"id", "display_name").Where(
		builder.In("id", iDs)).From(cons.NOTEBOOK_TABLE_NAME)
	err = n.Data.Engine.SQL(sqlStr).Find(&nbList)
	if err != nil {
		return nil, err
	}

	return nbList, nil
}

func (n *NotebookRepoImpl) GetNotebooksList(
	workspaceId int64, nbType string) ([]*model.Notebook, error) {

	var (
		nbList []*model.Notebook
		err    error
	)
	if nbType == cons.NOTEBOOK {
		if workspaceId != 0 {
			err = n.Data.Engine.SQL(
				"select * from notebook where workspace_id = ? ",
				workspaceId).Find(&nbList)
		} else {
			err = n.Data.Engine.SQL("select * from notebook").Find(&nbList)
		}
	}

	if err != nil {
		return nil, err
	}
	return nbList, nil
}

func (n *NotebookRepoImpl) GetRepoData() *data.Data {
	return n.Data
}

func (n *NotebookRepoImpl) DeleteNotebook(
	ctx context.Context, nb *model.Notebook, session *xorm.Session) error {
	affected, err := session.Delete(nb)
	if err != nil {
		return err
	}

	n.Log.Infof("delete notebook id:%v, affected:%v", nb.Id, affected)
	return nil
}

func (n *NotebookRepoImpl) UpdateNbImageType(
	ctx context.Context, req *pb.CreateNotebookRequest) error {

	update, err := n.Data.Engine.SQL(
		"update notebook set image_type = ? where id = ?",
		req.ImageType, req.Id).Execute()
	if err != nil {
		return err
	}

	id, _ := update.LastInsertId()
	affected, _ := update.RowsAffected()
	n.Log.Infof("update notebook imageTYpe. id:%v, affected:%v", id, affected)
	return nil
}

// CreateDelayTask 创建延时任务
func (n *NotebookRepoImpl) CreateDelayTask(
	ctx context.Context, task *model.NotebookDelayTask) error {

	_, err := n.Data.Engine.InsertOne(task)
	if err != nil {
		n.Log.Errorf("Create delay task failed: %v", err)
		return err_v1.ErrorDbError(err.Error())
	}
	n.Log.Infof("Create delay task success: %v", task.TaskID)
	return nil
}

// UpdateDelayTaskStatus 更新延时任务状态
func (n *NotebookRepoImpl) UpdateDelayTaskStatus(
	ctx context.Context, taskID, status, errorMsg string) error {

	updateData := map[string]interface{}{
		"status":      status,
		"update_time": "NOW()",
	}
	if errorMsg != "" {
		updateData["error_msg"] = errorMsg
	}

	_, err := n.Data.Engine.Table("notebook_delay_task").Where(
		"task_id = ?", taskID).Update(updateData)
	if err != nil {
		n.Log.Errorf("Update delay task status failed: %v", err)
		return err_v1.ErrorDbError(err.Error())
	}
	n.Log.Infof("Update delay task status success: %v -> %v", taskID, status)
	return nil
}

// GetPendingDelayTasks 获取待执行的延时任务
func (n *NotebookRepoImpl) GetPendingDelayTasks(
	ctx context.Context) ([]*model.NotebookDelayTask, error) {

	var tasks []*model.NotebookDelayTask
	err := n.Data.Engine.Where("status = ? AND execute_time <= NOW()", "pending").
		OrderBy("execute_time ASC").Find(&tasks)
	if err != nil {
		n.Log.Errorf("Get pending delay tasks failed: %v", err)
		return nil, err_v1.ErrorDbError(err.Error())
	}
	return tasks, nil
}

// CancelDelayTasksByNotebook 取消指定notebook的延时任务
func (n *NotebookRepoImpl) CancelDelayTasksByNotebook(
	ctx context.Context, notebookID, tenantID, workspaceID string) error {

	updateData := map[string]interface{}{
		"status":      "cancelled",
		"update_time": "NOW()",
	}

	_, err := n.Data.Engine.Table("notebook_delay_task").Where(
		"notebook_id = ? AND tenant_id = ? AND workspace_id = ? AND status = ?",
		notebookID, tenantID, workspaceID, "pending").Update(updateData)
	if err != nil {
		n.Log.Errorf("Cancel delay tasks failed: %v", err)
		return err_v1.ErrorDbError(err.Error())
	}
	n.Log.Infof("Cancel delay tasks success for notebook: %v", notebookID)
	return nil
}

// DeleteDelayTask 删除延时任务
func (n *NotebookRepoImpl) DeleteDelayTask(
	ctx context.Context, taskID string) error {

	_, err := n.Data.Engine.Where("task_id = ?", taskID).Delete(
		&model.NotebookDelayTask{})
	if err != nil {
		n.Log.Errorf("Delete delay task failed: %v", err)
		return err_v1.ErrorDbError(err.Error())
	}
	n.Log.Infof("Delete delay task success: %v", taskID)
	return nil
}
