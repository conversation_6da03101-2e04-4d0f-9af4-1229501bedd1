package biz

import (
	"context"
	"testing"
	"time"

	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func (m *MockNotebookRepo) UpdateNotebook(ctx context.Context, nb *model.Notebook) error {
	return nil
}

func (m *MockNotebookRepo) UpdateNotebookFull(ctx context.Context, nb *model.Notebook) error {
	return nil
}

func (m *MockNotebookRepo) UpdateNotebookName(ctx context.Context, req interface{}) error {
	return nil
}

func (m *MockNotebookRepo) UpdateNbImageType(ctx context.Context, req interface{}) error {
	return nil
}

func (m *MockNotebookRepo) UpdateNbStatusById(status, nbId string) error {
	return nil
}

func (m *MockNotebookRepo) GetSession() interface{} {
	return nil
}

func (m *MockNotebookRepo) GetRepoData() interface{} {
	return nil
}

func (m *MockNotebookRepo) GetNotebookByName(ctx context.Context, nb *model.Notebook) (*model.Notebook, error) {
	return nil, nil
}

func (m *MockNotebookRepo) CheckNotebookDisplayName(ctx context.Context, name string, workspaceId, nbcId int64) (int, error) {
	return 0, nil
}

func (m *MockNotebookRepo) GetNotebookById(ctx context.Context, notebookId string) (*model.Notebook, error) {
	return nil, nil
}

func (m *MockNotebookRepo) GetNotebookByNbcId(ctx context.Context, nbcId int64) ([]*model.Notebook, error) {
	return nil, nil
}

func (m *MockNotebookRepo) CountNotebookInWorkspace(ctx context.Context, nb *model.Notebook, nbType string, req interface{}) (int64, error) {
	return 0, nil
}

func (m *MockNotebookRepo) CountNotebookInWorkspaces(ctx context.Context, workspaceIds []uint64) (int64, error) {
	return 0, nil
}

func (m *MockNotebookRepo) ListNbListInWorkspace(ctx context.Context, nb *model.Notebook, nbType string, req interface{}) ([]*model.Notebook, error) {
	return nil, nil
}

func (m *MockNotebookRepo) ListNotebooksInWorkspace(ctx context.Context, nb *model.Notebook, nbType string) ([]*model.Notebook, error) {
	return nil, nil
}

func (m *MockNotebookRepo) ListNotebookInWorkspaces(ctx context.Context, workspaceIds []uint64, pagination *model.Pagination) ([]*model.Notebook, error) {
	return nil, nil
}

func (m *MockNotebookRepo) GetNbByIds(ctx context.Context, iDs []interface{}) ([]*model.Notebook, error) {
	return nil, nil
}

func (m *MockNotebookRepo) GetNotebookDisplayNameById(ctx context.Context, iDs []interface{}) ([]*model.Notebook, error) {
	return nil, nil
}

func (m *MockNotebookRepo) GetNotebooksList(workspaceId int64, nbType string) ([]*model.Notebook, error) {
	return nil, nil
}

// 添加其他接口方法的空实现...
func (m *MockNotebookRepo) CreateCollection(ctx context.Context, nbc *model.NotebookCollection, session interface{}) (int64, error) {
	return 0, nil
}

func (m *MockNotebookRepo) DeleteNbCollection(ctx context.Context, req interface{}, session interface{}) error {
	return nil
}

func (m *MockNotebookRepo) UpdateCollection(ctx context.Context, nbc *model.NotebookCollection, session interface{}, oldName, newName string) (int64, error) {
	return 0, nil
}

func (m *MockNotebookRepo) CheckNbCollectionName(ctx context.Context, req interface{}, workspaceId int64) (int, error) {
	return 0, nil
}

func (m *MockNotebookRepo) CountCollection(ctx context.Context, req interface{}, workspaceId int64) (int, error) {
	return 0, nil
}

func (m *MockNotebookRepo) Creators(ctx context.Context, workspaceId int64) ([]*model.Creator, error) {
	return nil, nil
}

func (m *MockNotebookRepo) GetNBCollectionByID(ctx context.Context, nbc *model.NotebookCollection, session interface{}) (*model.NotebookCollection, error) {
	return nil, nil
}

func (m *MockNotebookRepo) ListNbCollection(ctx context.Context, req interface{}, workspaceId int) ([]*model.NotebookCollection, error) {
	return nil, nil
}

func (m *MockNotebookRepo) SaveNbProductCommoditySpec(ctx context.Context, spec *model.NbProductCommoditySpecRelation) error {
	return nil
}

func (m *MockNotebookRepo) GetNbProductCommoditySpecByNotebookId(ctx context.Context, notebookId int64) (*model.NbProductCommoditySpecRelation, error) {
	return nil, nil
}

func (m *MockNotebookRepo) UpdateNbProductCommoditySpecByNotebookId(ctx context.Context, spec *model.NbProductCommoditySpecRelation) error {
	return nil
}

func (m *MockNotebookRepo) DeleteNbProductCommoditySpecByNotebookId(ctx context.Context, notebookId int64) error {
	return nil
}

func (m *MockNotebookRepo) SaveSpecConfiguration(ctx context.Context, spec *model.NbSpecConfigurationRelation) error {
	return nil
}

func (m *MockNotebookRepo) BatchSaveSpecConfigurations(ctx context.Context, specs []*model.NbSpecConfigurationRelation) error {
	return nil
}

func (m *MockNotebookRepo) DeleteSpecConfigurationByNotebookId(ctx context.Context, notebookId int64) error {
	return nil
}

func (m *MockNotebookRepo) GetSpecConfigurationByNotebookId(ctx context.Context, notebookId int64) ([]*model.NbSpecConfigurationRelation, error) {
	return nil, nil
}

// TestNewNotebookDelayTask 测试创建延时任务
func TestNewNotebookDelayTask(t *testing.T) {
	executeTime := time.Now().Add(10 * time.Minute)
	extraData := map[string]interface{}{
		"snapshot":    true,
		"channel":     "0",
		"receiverIDs": "user1,user2",
	}

	task := model.NewNotebookDelayTask(
		"notebook-123", 456, "tenant-1", "workspace-1", "user-1",
		executeTime, "pending", extraData)

	assert.NotEmpty(t, task.TaskID)
	assert.Equal(t, "notebook-123", task.NotebookID)
	assert.Equal(t, int64(456), task.NbcID)
	assert.Equal(t, "tenant-1", task.TenantID)
	assert.Equal(t, "workspace-1", task.WorkspaceID)
	assert.Equal(t, "user-1", task.UserID)
	assert.Equal(t, "pending", task.Status)
	assert.Equal(t, extraData, task.ExtraData)
	assert.WithinDuration(t, executeTime, task.ExecuteTime, time.Second)
}

// TestStoreDelayTask 测试存储延时任务
func TestStoreDelayTask(t *testing.T) {
	mockRepo := new(MockNotebookRepo)
	uc := &NotebookUsecase{
		nbRepo: mockRepo,
	}

	task := &model.NotebookDelayTask{
		TaskID:     "test-task-123",
		NotebookID: "notebook-123",
		Status:     "pending",
	}

	mockRepo.On("CreateDelayTask", mock.Anything, task).Return(nil)

	err := uc.storeDelayTask(task)
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
}
