package biz

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git-plat.tecorigin.net/ai-platform/backend-lib/casdoor"
	"git-plat.tecorigin.net/ai-platform/backend-lib/tecmq"
	pbV1 "git-plat.tecorigin.net/ai-platform/notebook-server/api/notebook/v1"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/cons"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/model"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/rpc"
	"git-plat.tecorigin.net/ai-platform/notebook-server/pkg/constEnum"

	"github.com/spf13/cast"
	"go.uber.org/zap"
)

func (uc *NotebookUsecase) mustStoreDelayTask(task *model.NotebookDelayTask) {
	err := uc.storeDelayTask(task)
	if err == nil {
		return
	}
	uc.Log.Errorf("Store delay task %v failed, retry later: %v", task.NotebookID, err)
	time.AfterFunc(constEnum.MUST_STORE_DELAY_TASK_BACKOFF_SECONDS*time.Second,
		func() { uc.mustStoreDelayTask(task) })
}

// 存储延时任务
func (uc *NotebookUsecase) storeDelayTask(task *model.NotebookDelayTask) error {
	ctx := context.Background()
	return uc.nbRepo.CreateDelayTask(ctx, task)
}

// 启动延时任务处理器
func (uc *NotebookUsecase) StartDelayTaskProcessor(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				uc.processDelayTasks(ctx)
			}
		}
	}()

	uc.Log.Info("延时任务处理器已启动~")
}

// 处理到期的延时任务
func (uc *NotebookUsecase) processDelayTasks(ctx context.Context) {
	// 尝试获取分布式锁（避免多实例同时处理）
	lockSuccess, err := uc.Rds.SetNX(ctx, cons.DELAY_TASK_LOCK_KEY, "1", 30*time.Second).Result()
	if err != nil || !lockSuccess {
		return
	}
	defer uc.Rds.Del(ctx, cons.DELAY_TASK_LOCK_KEY)

	// 查询已到期的任务
	tasks, err := uc.nbRepo.GetPendingDelayTasks(ctx)
	if err != nil || len(tasks) == 0 {
		return
	}

	// 处理每个到期任务
	for _, task := range tasks {
		// 检查任务状态，跳过已取消的任务
		if task.Status == "cancelled" {
			uc.Log.Infof("跳过已取消的任务: %v", task.TaskID)
			continue
		}

		// 更新任务状态为处理中
		err := uc.nbRepo.UpdateDelayTaskStatus(ctx, task.TaskID, "processing", "")
		if err != nil {
			uc.Log.Errorf("更新任务状态失败: %v", err)
			continue
		}

		// 从ExtraData中获取snapshot信息
		snapshot := false
		if task.ExtraData != nil {
			if val, ok := task.ExtraData["snapshot"]; ok {
				if snapVal, ok := val.(bool); ok {
					snapshot = snapVal
				}
			}
		}

		// 执行PatchNotebook操作
		request := &pbV1.PatchNotebookRequest{
			Status:     "stop",
			NoSnapshot: !snapshot,
			Id:         task.NotebookID,
			TenantId:   task.TenantID,
			NbcId:      task.NbcID,
		}
		wDetail := &model.WorkspaceDetail{
			WId: cast.ToUint64(task.WorkspaceID),
			TId: cast.ToUint64(task.TenantID),
		}
		user := &casdoor.User{
			Id: task.UserID,
		}

		nb, err := uc.PatchNotebook(ctx, request, user, wDetail, nil)
		if err != nil {
			uc.Log.Errorf("执行延时任务PatchNotebook失败 %v/%v: %v",
				request.NbcId, request.Id, err)
			// 标记任务为失败
			uc.nbRepo.UpdateDelayTaskStatus(ctx, task.TaskID, "failed", fmt.Sprintf("%v", err))
			continue
		}
		uc.Log.Infof("执行延时任务PatchNotebook成功 %v/%v",
			request.NbcId, request.Id)
		// 标记任务为成功
		uc.nbRepo.UpdateDelayTaskStatus(ctx, task.TaskID, "completed", "")
		// notify user.
		go uc.notifyUser(task, nb, cons.NotebookIdleReleaseTitle,
			cons.NotebookIdleReleaseTemplateId, nil)
	}
}

func (uc *NotebookUsecase) notifyUser(
	task *model.NotebookDelayTask, nb *model.Notebook,
	title string, templateId int, extraPayload map[string]interface{}) (
	err error) {

	// 0. log
	uc.Log.Infof("notifyUser for delay task: %v/%v/%v/%v(%v)",
		task.TenantID, task.WorkspaceID, task.NbcID, task.NotebookID, nb.DisplayName)
	defer func() {
		if err != nil {
			uc.Log.Errorf("notifyUser for delay task %v/%v/%v/%v(%v) failed: %v",
				task.TenantID, task.WorkspaceID, task.NbcID, task.NotebookID, nb.DisplayName, err)
		} else {
			uc.Log.Infof("notifyUser for delay task %v/%v/%v/%v(%v) success",
				task.TenantID, task.WorkspaceID, task.NbcID, task.NotebookID, nb.DisplayName)
		}
	}()

	// 1. get user info, workspace info, etc.
	ctx := context.TODO()

	// 从ExtraData中获取receiverIDs
	receiverIds := ""
	if task.ExtraData != nil {
		if val, ok := task.ExtraData["receiverIDs"]; ok {
			if receiverVal, ok := val.(string); ok {
				receiverIds = receiverVal
			}
		}
	}

	receiverIds = strings.Trim(receiverIds, " ")
	receiverIdsList := strings.Split(receiverIds, ",")
	receiversPb, err := rpc.GetReceiversAlertInfo(receiverIdsList)
	if err != nil {
		uc.Log.Errorf("Get receiver info %v failed: %v", receiverIds, err)
		return err
	}
	if len(receiversPb) == 0 {
		uc.Log.Warnf("notifyUser for delay task %v/%v/%v/%v(%v) no receiver",
			task.TenantID, task.WorkspaceID, task.NbcID, task.NotebookID, nb.DisplayName)
		return nil
	}
	workspaceDetails, err := rpc.WorkspaceDetail(ctx, []uint64{uint64(nb.WorkspaceId)})
	if err != nil {
		uc.Log.Errorf("Get workspace info %v failed: %v", nb.WorkspaceId, err)
		return err
	}
	nbc, err := uc.nbRepo.GetNBCollectionByID(ctx, &model.NotebookCollection{Id: task.NbcID}, nil)
	if err != nil {
		uc.Log.Errorf("Get nbc info %v failed: %v", task.NbcID, err)
		return err
	}

	// 2. format message
	payload := map[string]interface{}{
		"nbName":        nb.DisplayName,
		"workspaceName": workspaceDetails[0].Name,
		"nbcName":       nbc.Name,
	}
	for k, v := range extraPayload {
		payload[k] = v
	}
	// 从ExtraData中获取channel
	channel := ""
	if task.ExtraData != nil {
		if val, ok := task.ExtraData["channel"]; ok {
			if channelVal, ok := val.(string); ok {
				channel = channelVal
			}
		}
	}

	// send for every receiver id
	channelList := strings.Split(strings.Trim(channel, " "), ",")
	for _, receiver := range receiversPb {
		payload["userName"] = receiver.ReceiverName
		msgDataList := make([]*tecmq.MsgData, 0)
		for _, channel := range channelList {
			var address string
			if channel == "0" {
				address = receiver.Email
			} else if channel == "1" {
				address = receiver.Webhook
			}
			receivers := []tecmq.Receiver{
				{
					Id:      receiver.ReceiverId,
					Name:    receiver.ReceiverName,
					Address: address,
				},
			}

			msg := &tecmq.MsgData{
				Title:      title,
				Type:       cast.ToUint8(channel) + 1,
				Receivers:  receivers,
				TemplateId: templateId,
				Payload:    payload,
			}
			msgDataList = append(msgDataList, msg)
		}

		// 3. send message
		msg := &tecmq.Msgs{
			Source:           2,
			SourceCreateTime: time.Now(),
			Data:             msgDataList,
		}
		err = uc.mqRepo.Public(ctx, tecmq.DefaultMessageSubject, msg)
		if err != nil {
			uc.Log.Error("Send alert message failed", zap.Error(err))
		} else {
			uc.Log.Info("Send alert message success")
		}
	}

	return err
}
