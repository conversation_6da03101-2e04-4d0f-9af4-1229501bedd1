package biz

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git-plat.tecorigin.net/ai-platform/backend-lib/casdoor"
	err_v1 "git-plat.tecorigin.net/ai-platform/errno/api/err/v1"
	event_v1 "git-plat.tecorigin.net/ai-platform/event-server/api/eventEnum/v1"
	"git-plat.tecorigin.net/ai-platform/notebook-server/api/notebook/common"
	pb "git-plat.tecorigin.net/ai-platform/notebook-server/api/notebook/notebook"
	pbV2 "git-plat.tecorigin.net/ai-platform/notebook-server/api/notebook/v2"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/conf"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/cons"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/model"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/rpc"
	"git-plat.tecorigin.net/ai-platform/notebook-server/internal/util"
	consEnum "git-plat.tecorigin.net/ai-platform/notebook-server/pkg/constEnum"
	smModels "git-plat.tecorigin.net/ai-platform/scheduling-manager/models"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
)

func (uc *NotebookUsecase) CreateOptimizeNotebook(
	ctx context.Context, pDetail *model.WorkspaceDetail,
	req *pbV2.CreateOptimizeNotebookV2Request, notebookClient *NotebookClient,
	k8sClient *K8sClient, webIde *conf.WebIde,
	apisixConfig *conf.ApisixConfig, storageConfig *conf.Storage,
	user *casdoor.User, imageClient *conf.ImageClient) (int, []string, error) {

	successNum := 0
	createMsgs := make([]string, 0)
	for _, item := range req.ConfigList {
		if item.NotebookNum <= 0 {
			createMsgs = append(createMsgs, "notebookNum is invalid")
			continue
		}

		namespace := GetWorkspaceNameSpace(cast.ToString(pDetail.WId), item.ResourceMode)
		uc.Log.Info(namespace)
		for i := 0; i < cast.ToInt(item.NotebookNum); i++ {
			if !uc.nameReg.MatchString(item.ConfigName) {
				errStr := fmt.Sprintf(
					"CreateOptimizeNotebook item.ConfigName is invalid"+
						"(support 62 characters, _, number, -.). "+
						"item.ConfigName: %v",
					item.ConfigName)
				uc.Log.Error(errStr)
				createMsgs = append(createMsgs, errStr)
				continue
			}
			// check specConfiguration length
			if len(item.SpecConfiguration) < 2 {
				errStr := fmt.Sprintf(
					"CreateOptimizeNotebook item.SpecConfiguration is invalid"+
						"(support more than 2 specConfiguration2!). "+
						"item.SpecConfiguration: %v",
					item.SpecConfiguration)
				uc.Log.Error(errStr)
				createMsgs = append(createMsgs, errStr)
				continue
			}

			smSpecConfig := SpecConfigurationToSMSpecConfig(item.SpecConfiguration)
			quotaTypedUsage, err := smModels.NewQuotaTypedUsageFromSpecConfiguration(smSpecConfig)
			if err != nil {
				errStr := fmt.Sprintf(
					"CreateOptimizeNotebook NewQuotaTypedUsageFromSpecConfiguration err: %v", err)
				uc.Log.Error(errStr)
				createMsgs = append(createMsgs, errStr)
				continue
			}
			quotaValue := quotaTypedUsage.ToQuotaValue()
			memory, err := smModels.ConvertUnit(quotaValue.Memory, "BYTE", "GB")
			if err != nil {
				errStr := fmt.Sprintf(
					"CreateOptimizeNotebook ConvertUnit err: %v", err)
				uc.Log.Error(errStr)
				createMsgs = append(createMsgs, errStr)
				continue
			}

			nb := &model.Notebook{
				Name:        item.ConfigName + "-" + cast.ToString(i),
				Namespace:   namespace,
				WorkspaceId: cast.ToInt64(pDetail.WId),
				Image:       item.Image,
				Resource: &model.Resource{
					Cpu:    strconv.FormatUint(uint64(quotaValue.Cpu.Num)/1000, 10),
					Memory: fmt.Sprintf("%d%s", memory, "G"),
					Gpus:   &model.Gpus{},
				},
				ImageType: item.ImageType,
			}
			// 处理gpu资源
			for erType, v := range quotaValue.ExtendedResource {
				if v == 0 {
					continue
				}

				nb.Resource.Gpus.Num = strconv.FormatUint(v, 10)
				nb.Resource.Gpus.Vendor = erType.K8sResourceName()
				typeUsage := quotaTypedUsage.ExtendedResource[erType]
				for model, cnt := range typeUsage {
					if cnt == 0 {
						continue
					}
					nb.GpuVendorParam = model.String()
					break
				}
				break
			}

			item.NbcId = req.NbcId
			item.TenantId = cast.ToString(pDetail.TId)
			item.WorkspaceId = cast.ToString(pDetail.WId)
			reqV1 := util.ConvertV2ToV1Request(item, nb)
			createdNb, err := uc.CreateNotebook(
				ctx, nb, notebookClient, k8sClient, reqV1, webIde,
				apisixConfig, storageConfig, user, imageClient)
			if err != nil {
				errStr := fmt.Sprintf(
					"CreateOptimizeNotebook err :%v, %v", err, item)
				uc.Log.Error(errStr)
				createMsgs = append(createMsgs, errStr)
				continue
			}

			uc.Log.Infof("CreateOptimizeNotebook success createdNb: %v", createdNb)
			// spec configuration
			item.Id = strconv.FormatInt(createdNb.Id, 10)
			err = uc.SpecConfiguration.SaveNbProductCommoditySpec(item)
			if err != nil {
				uc.Log.Errorf("CreateOptimizeNotebook SaveNbProductCommoditySpec err :%v, %v", err, item)
			}
			err = uc.SpecConfiguration.SaveSpecConfiguration(item)
			if err != nil {
				uc.Log.Errorf("CreateOptimizeNotebook SaveSpecConfiguration err :%v, %v", err, item)
			}
			//暂记录创建实例成功数
			successNum = successNum + 1
			createMsgs = append(createMsgs, "create success")

			//操作审计
			rpc.CreateEvent(cons.CREATENOTEBOOK, cast.ToString(createdNb.Id),
				cast.ToString(pDetail.TId), event_v1.Event_EVENT_CREATE, user, req.NbcId)
		}
	}

	return successNum, createMsgs, nil
}

func (uc *NotebookUsecase) UpdateNotebookV2(
	ctx context.Context, req *pbV2.CreateNotebookV2Request,
	notebookClient *NotebookClient, k8sClient *K8sClient,
	pDetail *model.WorkspaceDetail, user *casdoor.User) (err error) {

	uc.Log.Infof("UpdateNotebookV2 req: %v", req)
	defer func() {
		if err != nil {
			uc.Log.Errorf("UpdateNotebookV2 %s(%s) err: %v", req.Name, req.Id, err)
		} else {
			uc.Log.Infof("UpdateNotebookV2 %s(%s) success", req.Name, req.Id)
		}
	}()

	oldNb, err := uc.nbRepo.GetNotebookById(ctx, req.Id)
	if err != nil {
		uc.Log.Errorf("UpdateNotebookV2 GetNotebookById err: %v", err)
		return err
	}
	newNb := &model.Notebook{}
	err = copier.Copy(newNb, oldNb)
	if err != nil {
		uc.Log.Errorf("UpdateNotebookV2 copier.Copy err: %v", err)
		return err
	}
	if len(req.SpecConfiguration) < 2 {
		uc.Log.Errorf("CreateOptimizeNotebook item.SpecConfiguration is "+
			"invalid(support more than 2 specConfiguration2!). item.SpecConfiguration:%v",
			req.SpecConfiguration)
		err = err_v1.ErrorNotebookResourceSpec(
			"specConfiguration is invalid(support more than 2 specConfiguration2!)")
		return err
	}

	smSpecConfig := SpecConfigurationToSMSpecConfig(req.SpecConfiguration)
	quotaTypedUsage, err := smModels.NewQuotaTypedUsageFromSpecConfiguration(smSpecConfig)
	if err != nil {
		uc.Log.Errorf("UpdateNotebookV2 NewQuotaTypedUsageFromSpecConfiguration err: %v", err)
		return err_v1.ErrorNotebookResourceSpec(
			"NewQuotaTypedUsageFromSpecConfiguration err: %v", err)
	}

	quotaValue := quotaTypedUsage.ToQuotaValue()
	memory, err := smModels.ConvertUnit(quotaValue.Memory, "BYTE", "GB")
	if err != nil {
		uc.Log.Errorf("UpdateNotebookV2 ConvertUnit err: %v", err)
		return err_v1.ErrorNotebookResourceSpec("ConvertUnit err: %v", err)
	}

	newNb.Resource = &model.Resource{
		Cpu:    strconv.FormatUint(uint64(quotaValue.Cpu.Num)/1000, 10),
		Memory: fmt.Sprintf("%d%s", memory, "G"),
		Gpus:   &model.Gpus{},
	}
	// 处理gpu资源
	for erType, v := range quotaValue.ExtendedResource {
		if v == 0 {
			continue
		}
		newNb.Resource.Gpus.Num = strconv.FormatUint(v, 10)
		newNb.Resource.Gpus.Vendor = erType.K8sResourceName()
		typeUsage := quotaTypedUsage.ExtendedResource[erType]
		for model, cnt := range typeUsage {
			if cnt == 0 {
				continue
			}
			newNb.GpuVendorParam = model.String()
			break
		}
		break
	}

	//update other field
	newNb.Cpu = newNb.Resource.Cpu
	newNb.Memory = newNb.Resource.Memory
	newNb.GpuNum = newNb.Resource.Gpus.Num
	newNb.GpuVendor = newNb.Resource.Gpus.Vendor
	newNb.Image = req.Image
	newNb.ImageType = req.ImageType
	newNb.WorkspaceId = int64(pDetail.WId)

	reqV1 := util.ConvertV2ToV1Request(req, newNb)
	err = uc.UpdateNotebook(ctx, reqV1, pDetail, user, oldNb, newNb)
	if err != nil {
		uc.Log.Errorf("UpdateNotebookV2 UpdateNotebook err: %v", err)
		return err
	}
	// spec configuration
	err = uc.SpecConfiguration.UpdateNbProductCommoditySpec(req)
	if err != nil {
		uc.Log.Errorf("UpdateNotebookV2 UpdateNbProductCommoditySpec err: %v", err)
	}
	err = uc.SpecConfiguration.UpdateSpecConfiguration(req)
	if err != nil {
		uc.Log.Errorf("UpdateNotebookV2 UpdateSpecConfiguration err: %v", err)
	}
	return nil
}

func (uc *NotebookUsecase) SetNotebookProductAndSpec(
	ctx context.Context, notebookItem *pb.NotebookItem) error {

	getNbProductInfo, err := uc.nbRepo.GetNbProductCommoditySpecByNotebookId(
		ctx, notebookItem.Id)
	if err != nil {
		uc.Log.Error("SetNotebookProductAndSpec err :%v", err)
		return err
	}
	if getNbProductInfo == nil {
		return nil
	}

	notebookItem.ResourceMode = getNbProductInfo.ResourceMode
	notebookItem.BillingModeId = getNbProductInfo.BillingModeId
	notebookItem.ProductId = getNbProductInfo.ProductId
	notebookItem.CommodityId = getNbProductInfo.CommodityId
	notebookItem.SpecFamilyId = getNbProductInfo.SpecFamilyId
	notebookItem.SpecId = getNbProductInfo.SpecId
	// spec configurations
	scList, err := uc.nbRepo.GetSpecConfigurationByNotebookId(ctx, notebookItem.Id)
	if err != nil {
		uc.Log.Error("SetNotebookProductAndSpec err :%v", err)
		return err
	}
	notebookItem.SpecConfiguration = make([]*pb.SpecConfiguration, 0)
	for _, sc := range scList {
		scItem := &pb.SpecConfiguration{
			Name:      sc.Name,
			Type:      sc.Type,
			Unit:      sc.Unit,
			Enable:    sc.Enable,
			ValueType: sc.ValueType,
			Value:     sc.Value,
			Id:        sc.ConfigurationId,
		}
		notebookItem.SpecConfiguration = append(notebookItem.SpecConfiguration, scItem)
	}
	return nil
}

func (uc *NotebookUsecase) ReleaseIdleNotebook(
	ctx context.Context, req *common.AlertGroupMessage) error {

	for _, alert := range req.AlertGroup.Alerts {
		uc.Log.Debugf("ReleaseIdleNotebook alert: %v", alert)

		// 处理任务取消逻辑（当alert.Status为"resolved"时）
		if alert.Status == consEnum.Resolved.String() {
			uc.Log.Info("ReleaseIdleNotebook alert status is resolved, cancelling tasks")

			// 获取notebook相关信息用于取消任务
			nbId, ok := alert.Labels["label_job_name"]
			if !ok || nbId == "" {
				uc.Log.Errorf("ReleaseIdleNotebook resolved alert job name is empty, skip")
				continue
			}
			tenantIdStr, ok := alert.Labels["label_tenant_id"]
			if !ok || tenantIdStr == "" {
				uc.Log.Errorf("ReleaseIdleNotebook resolved alert tenant id is empty, skip")
				continue
			}
			wIdStr, ok := alert.Labels["label_workspace_id"]
			if !ok || wIdStr == "" {
				uc.Log.Errorf("ReleaseIdleNotebook resolved alert workspace id is empty, skip")
				continue
			}

			// 取消对应的延时任务
			err := uc.nbRepo.CancelDelayTasksByNotebook(ctx, nbId, tenantIdStr, wIdStr)
			if err != nil {
				uc.Log.Errorf("Cancel delay tasks failed for notebook %v: %v", nbId, err)
			} else {
				uc.Log.Infof("Successfully cancelled delay tasks for notebook %v", nbId)
			}
			continue
		}

		//1. filter the useless message
		if alert.Status != consEnum.Firing.String() {
			uc.Log.Info("ReleaseIdleNotebook alert status is not firing, skip")
			continue
		}
		// type="idel"
		alertType, ok := alert.Labels["type"]
		if !ok || alertType != "idel" {
			uc.Log.Info("ReleaseIdleNotebook alert type is not idel, skip")
			continue
		}
		jobType, ok := alert.Labels["label_job_type"]
		if !ok || jobType != cons.LABEL_JOB_TYPE_OPTIMIZE {
			uc.Log.Info("ReleaseIdleNotebook alert job type is not optimize, skip")
			continue
		}
		status, ok := alert.Labels["status"]
		if !ok || status == "0" {
			uc.Log.Info("ReleaseIdleNotebook alert status is 0, skip")
			continue
		}
		uc.Log.Infof("ReleaseIdleNotebook alert: %v", alert)
		// 2. get the meta info
		nbId, ok := alert.Labels["label_job_name"] // notebook id
		if !ok || nbId == "" {
			uc.Log.Errorf("ReleaseIdleNotebook alert job name is empty, skip")
			continue
		}
		nbcId, ok := alert.Labels["label_nbc_id"]
		if !ok || nbcId == "" {
			uc.Log.Errorf("ReleaseIdleNotebook alert nbc id is empty, skip")
			continue
		}
		tenantIdStr, ok := alert.Labels["label_tenant_id"]
		if !ok || tenantIdStr == "" {
			uc.Log.Errorf("ReleaseIdleNotebook alert tenant id is empty, skip")
			continue
		}
		wIdStr, ok := alert.Labels["label_workspace_id"]
		if !ok || wIdStr == "" {
			uc.Log.Errorf("ReleaseIdleNotebook alert workspace id is empty, skip")
			continue
		}
		// release.  0: 保存实例镜像(可恢复数据) 1: 直接释放实例(可快速启动)
		release, ok := alert.Labels["release"]
		if !ok || release == "" {
			uc.Log.Errorf("ReleaseIdleNotebook alert release is empty, skip")
			continue
		}
		snapshot := release == "0"
		userId, ok := alert.Labels["user_id"]
		if !ok || userId == "" {
			uc.Log.Errorf("ReleaseIdleNotebook alert user id is empty, skip")
			continue
		}
		notifyPolicy := model.ParseNotifyPolicy(alert.Labels["notify"])
		notifyBefore := notifyPolicy&model.NotifyPolicyBeforeMask != model.NotifyPolicyNone
		delayMinStr := "0"
		if notifyBefore {
			delayMinStr, ok = alert.Labels["notify_before"]
			if !ok || delayMinStr == "" {
				uc.Log.Errorf("ReleaseIdleNotebook alert notify before is empty, skip")
				continue
			}
		}
		channel := alert.Labels["channel"]
		receivers := alert.Labels["receiver_id"]

		// 构建ExtraData
		extraData := map[string]interface{}{
			"snapshot":    snapshot,
			"notify":      notifyPolicy,
			"channel":     channel,
			"receiverIDs": receivers,
		}

		delayTask := model.NewNotebookDelayTask(
			nbId, cast.ToInt64(nbcId), tenantIdStr, wIdStr, userId,
			time.Now().Add(cast.ToDuration(delayMinStr)*time.Minute),
			"pending", extraData)

		// 3. only notify, not execute delay task
		if status == "2" {
			getNb, err := uc.nbRepo.GetNotebookById(ctx, delayTask.NotebookID)
			if err != nil {
				uc.Log.Errorf("GetNotebookById err before releasing notify: %v", err.Error())
				continue
			}

			go uc.notifyUser(delayTask, getNb, cons.NotebookIdleNotifyTitle,
				cons.NotebookIdleNotifyTemplateId, nil)
			uc.Log.Infof("ReleaseIdleNotebook alert status is 3, only notify, not execute delay task")
			continue
		}

		// 4. 创建延时任务并存入数据库
		uc.mustStoreDelayTask(delayTask)

		// 5. notify user before releasing
		if notifyBefore {
			getNb, err := uc.nbRepo.GetNotebookById(ctx, delayTask.NotebookID)
			if err != nil {
				uc.Log.Errorf("GetNotebookById err before releasing notify: %v", err.Error())
				continue
			}
			payload := map[string]interface{}{
				"releaseTime": delayMinStr,
			}
			go uc.notifyUser(delayTask, getNb, cons.NotebookIdleReleasePreTitle,
				cons.NotebookIdleReleasePreTemplateId, payload)
		}
	}
	return nil
}
