package model

import (
	"time"

	"github.com/google/uuid"
)

// NotebookDelayTask 表示一个延时执行的Notebook任务
type NotebookDelayTask struct {
	ID          int64                  `xorm:"pk autoincr comment('ID') bigint 'id'" json:"id"`
	TaskID      string                 `xorm:"not null unique varchar(255) 'task_id'" json:"task_id"`      // 任务唯一标识
	NotebookID  string                 `xorm:"not null varchar(255) 'notebook_id' index" json:"notebook_id"`  // Notebook ID
	NbcID       int64                  `xorm:"not null bigint 'nbc_id'" json:"nbc_id"`       // Notebook Collection ID
	TenantID    string                 `xorm:"not null varchar(255) 'tenant_id' index" json:"tenant_id"`    // 租户ID
	WorkspaceID string                 `xorm:"not null varchar(255) 'workspace_id' index" json:"workspace_id"` // 工作空间ID
	UserID      string                 `xorm:"not null varchar(255) 'user_id'" json:"user_id"`      // 触发用户ID
	CreateTime  time.Time              `xorm:"not null timestamp 'create_time'" json:"create_time"`  // 创建时间
	ExecuteTime time.Time              `xorm:"not null timestamp 'execute_time' index" json:"execute_time"` // 执行时间
	Status      string                 `xorm:"not null varchar(50) 'status' index" json:"status"`       // 任务状态：pending, processing, completed, failed, cancelled
	ExtraData   map[string]interface{} `xorm:"json 'extra_data'" json:"extra_data"`   // 额外的动态数据，存储为JSON
	ErrorMsg    string                 `xorm:"text 'error_msg'" json:"error_msg"`     // 错误信息
	UpdateTime  time.Time              `xorm:"not null timestamp 'update_time'" json:"update_time"`  // 更新时间
}

// TableName 返回表名
func (ndt *NotebookDelayTask) TableName() string {
	return "notebook_delay_task"
}

func NewNotebookDelayTask(
	notebookId string, nbcId int64,
	tenantId string, workspaceId string, userId string,
	executeTime time.Time, status string, extraData map[string]interface{}) *NotebookDelayTask {

	now := time.Now()
	return &NotebookDelayTask{
		TaskID:      uuid.New().String(),
		NotebookID:  notebookId,
		NbcID:       nbcId,
		TenantID:    tenantId,
		WorkspaceID: workspaceId,
		UserID:      userId,
		CreateTime:  now,
		ExecuteTime: executeTime,
		Status:      status,
		ExtraData:   extraData,
		UpdateTime:  now,
	}
}
