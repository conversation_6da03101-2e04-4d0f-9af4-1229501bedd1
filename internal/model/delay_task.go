package model

import (
	"github.com/google/uuid"
)

// NotebookDelayTask 表示一个延时执行的Notebook任务
type NotebookDelayTask struct {
	TaskID      string       `json:"task_id"`      // 任务唯一标识
	NotebookID  string       `json:"notebook_id"`  // Notebook ID
	NbcID       int64        `json:"nbc_id"`       // Notebook Collection ID
	TenantID    string       `json:"tenant_id"`    // 租户ID
	WorkspaceID string       `json:"workspace_id"` // 工作空间ID
	UserID      string       `json:"user_id"`      // 触发用户ID
	CreateTime  int64        `json:"create_time"`  // 创建时间（Unix时间戳）
	ExecuteTime int64        `json:"execute_time"` // 执行时间（Unix时间戳）
	Notify      NotifyPolicy `json:"notify"`       // 通知策略
	Channel     string       `json:"channel"`      // 通知方式：0: 邮箱  1:WEBHOOK   1,0：邮箱+WEBHOOK
	ReceiverIDs string       `json:"receiverIDs"`  // 待通知人：通知用户的 receiverIDs，逗号隔开的字符串
	Status      string       `json:"status"`       // 任务状态：pending, processing, completed, failed

	Snapshot bool   `json:"snapshot"` // 是否需要快照
	Reason   string `json:"reason"`   // 任务原因
}

func NewNotebookDelayTask(
	notebookId string, nbcId int64,
	tenantId string, workspaceId string, userId string,
	snapshot bool, createTime int64,
	executeTime int64, status string, notify NotifyPolicy,
	channel, receiverIDs string) *NotebookDelayTask {

	return &NotebookDelayTask{
		TaskID:      uuid.New().String(),
		NotebookID:  notebookId,
		NbcID:       nbcId,
		TenantID:    tenantId,
		WorkspaceID: workspaceId,
		UserID:      userId,
		Snapshot:    snapshot,
		CreateTime:  createTime,
		ExecuteTime: executeTime,
		Status:      status,
		Notify:      notify,
		Channel:     channel,
		ReceiverIDs: receiverIDs,
	}
}
