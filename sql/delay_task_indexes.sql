-- 延时任务表索引创建脚本
-- 执行此脚本来为notebook_delay_task表创建必要的索引以优化查询性能

-- 检查表是否存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables 
                   WHERE table_schema = 'public' 
                   AND table_name = 'notebook_delay_task') THEN
        RAISE EXCEPTION '表 notebook_delay_task 不存在，请先运行应用程序进行表同步';
    END IF;
END $$;

-- 创建主要索引
-- 任务ID唯一索引（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE tablename = 'notebook_delay_task' 
                   AND indexname = 'idx_task_id') THEN
        CREATE UNIQUE INDEX idx_task_id ON notebook_delay_task(task_id);
        RAISE NOTICE '创建索引: idx_task_id';
    ELSE
        RAISE NOTICE '索引 idx_task_id 已存在';
    END IF;
END $$;

-- Notebook ID索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE tablename = 'notebook_delay_task' 
                   AND indexname = 'idx_notebook_id') THEN
        CREATE INDEX idx_notebook_id ON notebook_delay_task(notebook_id);
        RAISE NOTICE '创建索引: idx_notebook_id';
    ELSE
        RAISE NOTICE '索引 idx_notebook_id 已存在';
    END IF;
END $$;

-- 租户ID索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE tablename = 'notebook_delay_task' 
                   AND indexname = 'idx_tenant_id') THEN
        CREATE INDEX idx_tenant_id ON notebook_delay_task(tenant_id);
        RAISE NOTICE '创建索引: idx_tenant_id';
    ELSE
        RAISE NOTICE '索引 idx_tenant_id 已存在';
    END IF;
END $$;

-- 工作空间ID索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE tablename = 'notebook_delay_task' 
                   AND indexname = 'idx_workspace_id') THEN
        CREATE INDEX idx_workspace_id ON notebook_delay_task(workspace_id);
        RAISE NOTICE '创建索引: idx_workspace_id';
    ELSE
        RAISE NOTICE '索引 idx_workspace_id 已存在';
    END IF;
END $$;

-- 执行时间索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE tablename = 'notebook_delay_task' 
                   AND indexname = 'idx_execute_time') THEN
        CREATE INDEX idx_execute_time ON notebook_delay_task(execute_time);
        RAISE NOTICE '创建索引: idx_execute_time';
    ELSE
        RAISE NOTICE '索引 idx_execute_time 已存在';
    END IF;
END $$;

-- 状态索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE tablename = 'notebook_delay_task' 
                   AND indexname = 'idx_status') THEN
        CREATE INDEX idx_status ON notebook_delay_task(status);
        RAISE NOTICE '创建索引: idx_status';
    ELSE
        RAISE NOTICE '索引 idx_status 已存在';
    END IF;
END $$;

-- 复合索引：用于任务取消查询
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE tablename = 'notebook_delay_task' 
                   AND indexname = 'idx_notebook_tenant_workspace_status') THEN
        CREATE INDEX idx_notebook_tenant_workspace_status 
        ON notebook_delay_task(notebook_id, tenant_id, workspace_id, status);
        RAISE NOTICE '创建索引: idx_notebook_tenant_workspace_status';
    ELSE
        RAISE NOTICE '索引 idx_notebook_tenant_workspace_status 已存在';
    END IF;
END $$;

-- 复合索引：用于待处理任务查询
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                   WHERE tablename = 'notebook_delay_task' 
                   AND indexname = 'idx_status_execute_time') THEN
        CREATE INDEX idx_status_execute_time 
        ON notebook_delay_task(status, execute_time);
        RAISE NOTICE '创建索引: idx_status_execute_time';
    ELSE
        RAISE NOTICE '索引 idx_status_execute_time 已存在';
    END IF;
END $$;

-- 显示所有创建的索引
SELECT 
    indexname as "索引名称",
    indexdef as "索引定义"
FROM pg_indexes 
WHERE tablename = 'notebook_delay_task'
ORDER BY indexname;

-- 显示表统计信息
SELECT 
    schemaname as "模式",
    tablename as "表名",
    attname as "列名",
    n_distinct as "唯一值数量",
    correlation as "相关性"
FROM pg_stats 
WHERE tablename = 'notebook_delay_task'
ORDER BY attname;

RAISE NOTICE '延时任务表索引创建完成！';
